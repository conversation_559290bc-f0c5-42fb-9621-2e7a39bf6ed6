{"general": {"search": "Search...", "select": "Select", "buttons": {"save": "Save", "confirm": "Confirm", "cancel": "Cancel", "delete": "Delete", "create": "Create", "update": "Update", "complete": "Complete", "review": "Review", "reset": "Reset", "add": "Add", "overview": "Overview", "accept": "Accept", "reject": "Reject", "edit": "Edit", "done": "Done", "preview": "Preview", "share": "Share", "back_to_portal": "Back to portal", "next": "Next", "previous": "Previous", "mark_complete": "<PERSON><PERSON>", "close": "Close", "change_selection": "Change selection", "mark_as_complete": "Mark as complete", "remove": "Remove", "retry": "Retry", "refresh": "Refresh", "send": "Send", "login": "<PERSON><PERSON>", "register": "Register", "select_all": "Select all", "deselect_all": "Deselect all", "upload": "Upload"}, "selected_all": "All selected", "unknown": "Unknown", "back": "Back", "toasts": {"error": {"title": "Oops, something went wrong!", "description": "It looks like something went wrong on our end. Please try again or contact our support."}, "success": {"title": "Success!", "description": "The action you performed has been successfully completed."}}, "bool": {"true": "Yes", "false": "No"}, "manage": "Manage", "empty_data": "It looks like there is no data available.", "from": "From", "until": "Until", "to": "To", "filter": "Filter", "sort": "Sort by", "settings": "Settings", "request_support": "Request support", "date": "Date", "google": "Google", "microsoft": "Microsoft", "facebook": "Facebook", "linkedin": "LinkedIn", "pinterest": "Pinterest", "tiktok": "TikTok", "instagram": "Instagram", "connected": "Connected", "error": "Error", "preview_mode": "Preview mode. Open in incognito to exit preview mode.", "skipped": "Skipped", "skip": "<PERSON><PERSON>", "synced": "Synced", "connect": "Connect", "failed": "Failed", "failed_data": "Successful", "success": "Success", "pending": "Pending", "connect_your_accounts": "Connect your accounts", "add_another": "Add another one", "skipped_drawer": {"title": "Connection skipped!", "description": "It looks like you have skipped this connection."}, "syncing": "Syncing", "empty_placeholder": "There are no entry's found"}, "pages": {"auth": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "forgot": "Forgot your password?"}, "password": {"forgot": {"title": "Forgot password?", "request": "Request", "cancel": "Cancel", "submitted": {"title": "Password reset request submitted!", "description": "Your submission for a password reset has been successfully received by us. If your email exists then you will receive an email to reset your password", "login": "Back to login"}, "inputs": {"email": "Email"}}, "reset": {"title": "Reset password", "submit": "Save", "cancel": "Cancel", "submitted": {"title": "Successfully reset password!", "description": "Your password has been successfully reset. You can now use your new password to login.", "login": "Back to login"}, "inputs": {"email": "Email", "password": "Password"}}}}, "admin": {"tabs": {"files": "Files", "users": "Users"}, "files": {"index": {"category": "Category", "upload": "Upload files"}, "upload": {"title": "Upload Images", "drag_drop_text": "Drag and drop images here, or", "click_to_browse": "click to browse", "supported_formats": "Supports JPG, PNG, GIF, WebP", "images_count": "{{count}} of {{total}} images", "category": "Category", "category_required": "Please select a category"}}, "user": {"index": {"table": {"id": "ID", "name": "Name", "email": "Email", "role": "Role", "last_login": "Last Login", "actions": "Actions", "never_logged_in": "Never logged in"}, "delete": {"title": "Delete user?", "description": "Are you sure you want to delete this user?"}}}, "users": {"detail": {"title_create": "Create User", "title_edit": "Edit User", "firstname": "First Name", "lastname": "Last Name", "email": "Email", "role": "Role", "role_placeholder": "Select a role"}}}}, "layouts": {"container": {"header": {"logout": "Logout"}, "sidebar": {"home": "Home", "admin": "Admin"}}}, "enums": {"file_category": {"CAMPAIGNS": "Campaigns", "AD_GROUPS": "Ad groups", "KEYWORDS": "Keywords", "SEARCH_TERMS": "Search terms", "AUDIENCES": "Audiences", "ADS": "Ads", "OTHER": "Other"}, "user_role": {"ADMIN": "Administrator", "USER": "User"}}, "components": {"file": {"delete": {"title": "Delete file?", "description": "Are you sure you want to delete this file?"}}, "pagination": {"next": "Next", "previous": "Previous"}}}