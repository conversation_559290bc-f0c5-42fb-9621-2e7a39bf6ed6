import { inject, Injectable } from '@angular/core';
import { ModalService } from '@app/services/modal.service';
import { take, tap } from 'rxjs';
import { File } from '@api/file/models/file.interface';
import { DeleteComponent } from '@components/file/delete/delete.component';

@Injectable({
  providedIn: 'root',
})
export class DeleteService {
  private modalService = inject(ModalService);

  public show(file: File): Promise<boolean> {
    const modal = this.modalService.attach(DeleteComponent);

    modal.componentRef.instance.file = file;

    return new Promise((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value: boolean) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
