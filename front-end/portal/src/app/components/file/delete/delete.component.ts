import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ModalComponent } from '@app/components/modal/modal.component';
import {
  genericToastError,
  genericToastSuccess,
} from '@app/helpers/generic-toasts';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import { catchError, Subject, tap } from 'rxjs';
import { FileService } from '@api/file/services/file.service';
import { File } from '@api/file/models/file.interface';

@Component({
  selector: 'app-delete',
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe],
  templateUrl: './delete.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteComponent {
  public file!: File;
  public loading = signal<boolean>(false);

  public close$: Subject<boolean> = new Subject();

  private fileService = inject(FileService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.file || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.fileService
      .delete(this.file)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.close(true);
          genericToastSuccess(this.toastService, this.translocoService);
        }),
        catchError((err) => {
          this.loading.set(false);
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
