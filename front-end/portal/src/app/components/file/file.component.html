<div class="relative group bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
  <!-- Image -->
  <div class="aspect-square bg-gray-100">
    <img [src]="file.url"
         [alt]="file.name"
         class="w-full h-full object-cover">
  </div>

  <!-- Image Info and Category -->
  <div class="p-3 space-y-3">
    <!-- Image Details -->
    <div>
      <p class="text-sm font-medium text-gray-900 truncate" [title]="file.name">
        {{ file.name }}
      </p>
    </div>
  </div>

  <div>
    @if(canDelete){
      <button class="absolute top-2 right-2 w-6 h-6 bg-adverge-red text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center hover:bg-adverge-red cursor-pointer"
              (click)="delete()"
              type="button">
        <i class="fa-regular fa-trash-can text-xs"></i>
      </button>
    }
  </div>

  <p class="absolute top-2 left-2 w-fit px-2 py-0.5 bg-adverge-blue text-white rounded-full text-sm">
    {{ 'enums.file_category.' + file.category | transloco }}
  </p>
</div>
