import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { File } from '@api/file/models/file.interface';
import { ReactiveFormsModule } from '@angular/forms';
import { SelectComponent } from '@components/form-inputs/select/select.component';
import { Subject } from 'rxjs';
import { DeleteService } from '@components/file/delete/delete.service';
import { TranslocoPipe } from '@jsverse/transloco';

@Component({
  selector: 'components-file',
  imports: [ReactiveFormsModule, SelectComponent, TranslocoPipe],
  templateUrl: './file.component.html',
})
export class FileComponent {
  @Input({ required: true }) file!: File;
  @Input() canDelete: boolean = false;
  @Output() reload$: EventEmitter<void> = new EventEmitter();

  private deleteService = inject(DeleteService);

  public async delete(): Promise<void> {
    const result = await this.deleteService.show(this.file);

    if (!result) {
      return;
    }

    this.reload$.emit();
  }
}
