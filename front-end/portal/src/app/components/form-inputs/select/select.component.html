@if (selectOpened) {
  <div class="fixed h-full w-full bg-black/20 left-0 top-0 z-[60] pointer-events-none"></div>
}

<div #container
     [ngClass]="{
      'z-[80]': selectOpened
     }"
     class="relative" *transloco="let t;">
  @if(label) {
    <label
      class=""
      [class.!text-red-500]="isInvalid"
    >
      {{ label }}
    </label>
  }

  <button
    (click)="toggleSelect()"
    type="button"
    class="relative w-full cursor-pointer border border-gray-300 rounded bg-white py-1.5 pl-4 pr-10 text-left font-light text-adverge-gray shadow-sm focus:outline-none focus:ring-1 focus:ring-adverge-blue sm:text-sm sm:leading-6"
    [class.!border-red-500]="isInvalid"
    [class.mt-2]="!!label"
  >
    <span class="inline-flex w-full truncate py-0.5 h-7">
      @if(searchOpened) {
        <input #searchValueControl [formControl]="searchValue" class="focus:outline-none w-full" type="text" [placeholder]="t('general.search')" />
      } @else {
        @if(selectedValues.length && (!multiple || selectedValues.length < data.length)) {
          @for(selectedValue of selectedValues; track selectedValue) {
            <span class="truncate">
              {{ selectedValue.label }}
            </span>
            <span class="ml-2 truncate text-gray-500">
              {{ selectedValue.description }}
            </span>
          }
        } @else if(selectedValues.length && selectedValues.length === data.length && multiple) {
          <span>{{t('general.selected_all')}}</span>
        } @else {
          <span class="font-light">
            {{ t('general.select') }}
          </span>
        }
      }
    </span>

    @if(selectOpened) {
      @if (!searchOpened) {
        <span (click)="toggleSearch(); $event.stopImmediatePropagation()" class="absolute inset-y-0 right-0 flex items-center pr-3">
          <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
          </svg>
        </span>
      } @else {
        <span (click)="toggleSearch(); $event.stopImmediatePropagation()" class="absolute inset-y-0 right-0 flex items-center pr-3">
          <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
          </svg>
        </span>
      }
    } @else {
      <span class="absolute inset-y-0 right-0 flex items-center pr-2">
        <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z" clip-rule="evenodd" />
        </svg>
      </span>
    }
  </button>



  @if(selectOpened) {
    <ul
      #scrollContainer
      (scroll)="onScroll($event)"
      class="absolute z-[80] mt-3 max-h-60 w-full overflow-auto rounded bg-white py-1.5 text-base shadow-lg ring-1 ring-adverge-blue focus:outline-none sm:text-sm"
      tabindex="-1"
      role="listbox"
    >
      @if(multiple) {
        <div class="flex justify-end px-2 py-1">
          <button (click)="selectOrDeselectAll()" class="link">{{ 'general.buttons.' + (selectedValues.length === data.length ? 'deselect_all' : 'select_all') | transloco }}</button>
        </div>
      }

      @for(item of data; track item) {
        <li
          (click)="selectValue(item)"
          class="group text-gray-900 relative cursor-pointer select-none px-2"
          role="option"
        >
          <div [class]="'flex group-hover:bg-gray-200/40 rounded py-3 px-2.5 ' + item.classes">
            <span
              class="font-normal truncate"
              [class.font-semibold]="containsValue(item)"
            >
              {{ item.label }}
            </span>
            @if(item.description) {
              <span class="text-gray-500 ml-2 truncate">
                {{ item.description }}
              </span>
            }
          </div>

          @if(containsValue(item)) {
            <span class="text-adverge-blue absolute inset-y-0 right-0 flex items-center pr-4">
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
              </svg>
            </span>
          }
        </li>
      }

      @if(isLoading && connected) {
        <li class="py-2 px-3 flex justify-center">
          <i class="fa-duotone fa-spinner-third animate-spin"></i>
        </li>
      }
    </ul>
  }
</div>
