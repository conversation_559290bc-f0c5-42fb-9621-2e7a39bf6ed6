import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { TranslocoDirective } from '@jsverse/transloco';

@Component({
  selector: 'components-pagination',
  imports: [TranslocoDirective],
  templateUrl: './pagination.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaginationComponent {
  @Input({ required: true }) response!: PaginatedResponse<any> | null;
  @Output() pageChange$: EventEmitter<number> = new EventEmitter();

  public pageChange(page: number): void {
    this.pageChange$.emit(page);
  }
}
