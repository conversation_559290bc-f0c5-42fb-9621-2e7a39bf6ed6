@if(response && response.meta.last_page > 1) {
  <nav *transloco="let t; read: 'components.pagination'" class="flex items-center justify-center space-x-4" aria-label="Pagination">
    @if(response.meta.current_page > 1) {
      <button (click)="pageChange((response?.meta?.current_page ?? 0) - 1)" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        <i class="fa-regular fa-chevron-left mr-2"></i>
        {{ t('previous') }}
      </button>
    }

    <span class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-900 bg-blue-50 border border-blue-200 rounded-md">
      {{ (response?.meta?.current_page ?? 0) }} / {{ response.meta.last_page }}
    </span>

    @if((response?.meta?.current_page ?? 0) < response.meta.last_page) {
      <button (click)="pageChange((response?.meta?.current_page ?? 0) + 1)" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        {{ t('next') }}
        <i class="fa-regular fa-chevron-right ml-2"></i>
      </button>
    }
  </nav>
}
