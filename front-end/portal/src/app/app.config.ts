import {
  ApplicationConfig,
  importProvidersFrom,
  inject,
  isDevMode,
  provideAppInitializer,
  provideZoneChangeDetection,
} from '@angular/core';
import {
  provideRouter,
  withComponentInputBinding,
  withHashLocation,
} from '@angular/router';
import en from '@angular/common/locales/en';
import { routes } from './app.routes';
import { provideHotToastConfig } from '@ngxpert/hot-toast';
import { registerLocaleData } from '@angular/common';
import { ConfigService } from '@services/config.service';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideTransloco } from '@jsverse/transloco';
import { unauthenticatedInterceptor } from '@interceptors/unauthenticated.interceptor';
import { TranslocoHttpLoader } from './transloco-loader';
import { FormsModule } from '@angular/forms';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';

registerLocaleData(en);

export let appConfig: ApplicationConfig;

appConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withHashLocation(), withComponentInputBinding()),
    provideAppInitializer(() => {
      const initializerFn = (
        (configService: ConfigService) => () =>
          configService.initialize().then(async ({}) => {})
      )(inject(ConfigService));
      return initializerFn();
    }),
    provideHttpClient(withInterceptors([unauthenticatedInterceptor])),
    provideTransloco({
      config: {
        availableLangs: ['en'],
        defaultLang: 'en',
        // Remove this option if your application doesn't support changing language in runtime.
        reRenderOnLangChange: true,
        prodMode: !isDevMode(),
      },
      loader: TranslocoHttpLoader,
    }),
    provideHotToastConfig({
      autoClose: true,
      className: 'toast-container',
      position: 'top-right',
      stacking: 'depth',
      dismissible: true,
    }),
    importProvidersFrom(FormsModule),
    provideAnimationsAsync(),
    provideHttpClient(),
  ],
};
