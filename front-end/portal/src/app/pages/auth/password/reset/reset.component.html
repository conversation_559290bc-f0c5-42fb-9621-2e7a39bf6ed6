<ng-container *transloco="let t; read: 'pages.auth.password.reset'">
  @if(!submitted) {
    <div class="text font-medium text-gray-700 -mt-2 mb-4">
      {{ t("title") }}
    </div>

    @if(form) {
      <form [formGroup]="form" (ngSubmit)="submit()" class="space-y-6 relative">
        <div class="form-group">
          <label>{{ t("inputs.email") }}</label>
          <input [formControl]="form.controls.email" type="email" autocomplete="email">
        </div>

        <div class="form-group">
          <label>{{ t("inputs.password") }}</label>
          <input [formControl]="form.controls.password" type="password" autocomplete="current-password" class="input">
        </div>

        <div class="flex justify-between items-center">
          <a routerLink="/auth/login" class="link">
            {{ t("cancel") }}
          </a>
          <button [disabled]="isLoading" [class.loading]="isLoading" type="submit" class="btn --blue --large">
            {{ t("submit") }}
          </button>
        </div>
      </form>
    }
  }

  @if (submitted) {
    <div *transloco="let t; read: 'pages.auth.password.reset.submitted'" class="space-y-4">
      <h2 class="text font-medium">{{ t('title') }}</h2>
      <p>{{ t('description') }}</p>
      <a routerLink="/auth/login" class="btn --blue --large">{{ t('login') }}</a>
    </div>
  }
</ng-container>
