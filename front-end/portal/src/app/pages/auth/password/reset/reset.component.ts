import { Component, DestroyRef, Input, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { tap, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { genericToastError } from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { RouterLink } from '@angular/router';
import { PasswordResetRequest } from '@api/auth/requests/password-reset.request';
import { PasswordService } from '@api/auth/services/password.service';
import { HotToastService } from '@ngxpert/hot-toast';

interface Form {
  email: FormControl<string>;
  password: FormControl<string>;
  token: FormControl<string>;
}

@Component({
  selector: 'app-reset',
  templateUrl: './reset.component.html',
  styleUrls: [],
  imports: [TranslocoDirective, ReactiveFormsModule, RouterLink],
})
export class ResetComponent implements OnInit {
  @Input('token') token: string | undefined;
  public form: FormGroup<Form> | undefined;
  public submitted: boolean = false;
  public isLoading = false;

  constructor(
    private translocoService: TranslocoService,
    private toastService: HotToastService,
    private passwordService: PasswordService,
    private destroyRef: DestroyRef,
  ) {}

  public ngOnInit(): void {
    this.initForm();
  }

  public submit(): void {
    if (!this.form || this.form.invalid) {
      return;
    }

    this.isLoading = true;

    this.passwordService
      .reset(this.form.value as PasswordResetRequest)
      .pipe(
        tap(() => {
          this.submitted = true;
          this.isLoading = false;
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.isLoading = false;
          return throwError(err);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    this.form = new FormGroup<Form>({
      email: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required],
      }),
      password: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required],
      }),
      token: new FormControl(this.token ?? '', {
        nonNullable: true,
        validators: [Validators.required],
      }),
    });
  }
}
