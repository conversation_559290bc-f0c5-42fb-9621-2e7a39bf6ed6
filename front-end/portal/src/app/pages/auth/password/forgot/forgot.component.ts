import { Component, DestroyRef, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { tap, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { genericToastError } from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { HotToastService } from '@ngxpert/hot-toast';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { RouterLink } from '@angular/router';
import { PasswordService } from '@api/auth/services/password.service';

interface Form {
  email: FormControl<string>;
}

@Component({
  selector: 'app-forgot',
  templateUrl: './forgot.component.html',
  styleUrls: [],
  standalone: true,
  imports: [TranslocoDirective, ReactiveFormsModule, RouterLink],
})
export class ForgotComponent implements OnInit {
  public form: FormGroup<Form> | undefined;
  public isLoading: boolean = false;
  public submitted: boolean = false;

  constructor(
    private passwordService: PasswordService,
    private translocoService: TranslocoService,
    private toastService: HotToastService,
    private destroyRef: DestroyRef,
  ) {}

  public ngOnInit(): void {
    this.initForm();
  }

  public submit(): void {
    if (!this.form || this.form.invalid) {
      return;
    }

    this.isLoading = true;

    this.passwordService
      .forgot(this.form.controls.email.value)
      .pipe(
        tap(() => {
          this.submitted = true;
          this.isLoading = false;
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.isLoading = false;
          return throwError(err);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    this.form = new FormGroup<Form>({
      email: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.email],
      }),
    });
  }
}
