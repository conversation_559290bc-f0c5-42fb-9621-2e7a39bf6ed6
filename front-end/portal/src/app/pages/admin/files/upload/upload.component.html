<div *transloco="let t; read: 'pages.admin.files.upload'">
  <div class="flex w-full justify-between items-center">
    <a routerLink="/admin/files" class="link space-x-2">
      <i class="fa-regular fa-arrow-left"></i>
      <span>{{ 'general.back' | transloco }}</span>
    </a>
    <button (click)="submit()" class="btn" [class.--disabled]="form.invalid">
      @if(saving()) {
        <div class="flex items-center space-x-3">
          <!-- Linear Progress Bar -->
          <div class="flex items-center space-x-2">
            <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                class="h-full bg-adverge-red transition-all duration-300 ease-in-out rounded-full"
                [style.width.%]="getUploadProgress()">
              </div>
            </div>
            <span class="text-xs font-medium text-gray-600 min-w-[2rem]">{{ getUploadProgress() }}%</span>
          </div>
        </div>
      } @else {
        <i class="fa-regular fa-floppy-disk"></i>
        <span>{{ 'general.buttons.upload' | transloco }}</span>
      }
    </button>
  </div>

  <!-- Upload Form -->
  <form [formGroup]="form" class="mt-8 space-y-6">
    <!-- Image Upload Section -->
    <div>
      <!-- Upload Area -->
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer"
           (click)="triggerFileInput()"
           (dragover)="onDragOver($event)"
           (dragleave)="onDragLeave($event)"
           (drop)="onDrop($event)">

        <div class="flex flex-col items-center space-y-4">
          <!-- Upload Icon -->
          <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <i class="fa-solid fa-upload text-blue-600 text-xl"></i>
          </div>

          <!-- Upload Text -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ t('title') }}</h3>
            <p class="text-gray-500">
              {{ t('drag_drop_text') }}
              <span class="text-blue-600 font-medium">{{ t('click_to_browse') }}</span>
            </p>
            <p class="text-sm text-gray-400 mt-1">{{ t('supported_formats') }}</p>
          </div>
        </div>

        <!-- Hidden File Input -->
        <input #fileInput
               type="file"
               multiple
               accept="image/*"
               class="hidden"
               (change)="onFileSelected($event)">
      </div>

      <!-- Image Preview Section -->
      @if (imagesFormArray.length > 0) {
        <div class="mt-8">
          <div class="flex items-center justify-between mb-4">
            <span class="text-sm text-gray-600">
              {{ t('images_count', { count: imagesFormArray.length, total: imagesFormArray.length }) }}
            </span>
          </div>

          <!-- Image Grid -->
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" formArrayName="images">
            @for (imageControl of imagesFormArray.controls; track imageControl.controls.image.value.id; let i = $index) {
              <div class="relative group bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow" [formGroupName]="i">
                <!-- Image -->
                <div class="aspect-square bg-gray-100">
                  <img [src]="imageControl.controls.image.value.preview"
                       [alt]="imageControl.controls.image.value.name"
                       class="w-full h-full object-cover">
                </div>

                <!-- Image Info and Category -->
                <div class="p-3 space-y-3">
                  <!-- Image Details -->
                  <div>
                    <p class="text-sm font-medium text-gray-900 truncate" [title]="imageControl.controls.image.value.name">
                      {{ imageControl.controls.image.value.name }}
                    </p>
                    <p class="text-xs text-gray-500 mt-1">
                      {{ formatFileSize(imageControl.controls.image.value.size) }}
                    </p>
                  </div>

                  <!-- Category Select -->
                  <div>
                    @if(categories(); as categories) {
                      <form-select
                        formControlName="category"
                        [data]="categories"
                        [label]="t('category')"
                        [isInvalid]="imageControl.controls.category.invalid && imageControl.controls.category.touched">
                      </form-select>
                    }
                  </div>
                </div>

                <!-- Remove Button -->
                <button class="absolute top-2 right-2 w-6 h-6 bg-adverge-red text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center hover:bg-adverge-red cursor-pointer"
                        (click)="removeImage(i)"
                        type="button">
                  <i class="fa-solid fa-times text-xs"></i>
                </button>
              </div>
            }
          </div>
        </div>
      }
    </div>
  </form>
</div>
