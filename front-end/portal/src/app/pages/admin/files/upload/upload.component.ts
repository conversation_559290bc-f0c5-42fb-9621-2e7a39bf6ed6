import {
  Component,
  DestroyRef,
  ElementRef,
  inject,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { Router, RouterLink } from '@angular/router';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { FileCategory } from '@api/file/enums/file-category.enum';
import { SelectOption } from '@interfaces/select-option.interface';
import { SelectComponent } from '@components/form-inputs/select/select.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FileService } from '@api/file/services/file.service';
import {
  genericToastError,
  genericToastSuccess,
} from '@helpers/generic-toasts';
import { catchError } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

interface UploadedImage {
  id: string;
  name: string;
  size: number;
  preview: string;
  file: File;
}

interface ImageForm {
  image: FormControl<UploadedImage>;
  category: FormControl<SelectOption<FileCategory> | null>;
}

interface UploadForm {
  images: FormArray<FormGroup<ImageForm>>;
}

@Component({
  selector: 'app-upload',
  imports: [
    TranslocoPipe,
    TranslocoDirective,
    RouterLink,
    ReactiveFormsModule,
    SelectComponent,
  ],
  templateUrl: './upload.component.html',
})
export class UploadComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  public form!: FormGroup<UploadForm>;
  public categories = signal<SelectOption<FileCategory>[]>([]);
  public saving = signal<boolean>(false);
  public processedFiles = signal<number>(0);

  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private fileService = inject(FileService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);

  public ngOnInit(): void {
    this.initForm();
    this.initCategories();
  }

  public triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  public onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.handleFiles(Array.from(input.files));
    }
  }

  public onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  public onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  public onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer?.files) {
      const files = Array.from(event.dataTransfer.files).filter((file) =>
        file.type.startsWith('image/'),
      );
      this.handleFiles(files);
    }
  }

  public removeImage(index: number): void {
    this.imagesFormArray.removeAt(index);
  }

  public get imagesFormArray(): FormArray<FormGroup<ImageForm>> {
    return this.form.controls.images;
  }

  public formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  public async submit(): Promise<void> {
    if (this.saving() || this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    this.saving.set(true);

    for (const file of this.form.controls.images.value) {
      if (!file.image) {
        continue;
      }
      try {
        // Simulate file upload progress for now since the service method doesn't exist yet
        await this.fileService
          .store({
            file: file.image.file,
            category: file.category?.value ?? FileCategory.OTHER,
          })
          .pipe(
            catchError((err) => {
              genericToastError(this.toastService, this.translocoService);
              return err;
            }),
            takeUntilDestroyed(this.destroyRef),
          )
          .toPromise();
      } catch {
        this.saving.set(false);
      } finally {
        this.processedFiles.set(this.processedFiles() + 1);
      }
    }

    this.saving.set(false);

    genericToastSuccess(this.toastService, this.translocoService);
    this.router.navigate(['admin', 'files']);
  }

  public getUploadProgress(): number {
    const totalFiles = this.form?.controls.images.value.length;
    if (totalFiles === 0) return 0;
    return Math.round((this.processedFiles() / totalFiles) * 100);
  }

  private initForm(): void {
    this.form = new FormGroup<UploadForm>({
      images: new FormArray<FormGroup<ImageForm>>([]),
    });
  }

  private initCategories(): void {
    this.categories.set(
      Object.values(FileCategory).map((category) => ({
        value: category,
        label: this.translocoService.translate(
          `enums.file_category.${category}`,
        ),
      })),
    );
  }

  private handleFiles(files: File[]): void {
    files.forEach((file) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const uploadedImage: UploadedImage = {
            id: this.generateId(),
            name: file.name,
            size: file.size,
            preview: e.target?.result as string,
            file: file,
          };

          const imageFormGroup = new FormGroup<ImageForm>({
            image: new FormControl(uploadedImage, { nonNullable: true }),
            category: new FormControl<SelectOption<FileCategory> | null>(null, [
              Validators.required,
            ]),
          });

          this.imagesFormArray.push(imageFormGroup);
        };
        reader.readAsDataURL(file);
      }
    });
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
