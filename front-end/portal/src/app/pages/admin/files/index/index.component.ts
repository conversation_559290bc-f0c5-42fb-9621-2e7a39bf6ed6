import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { FileCategory } from '@api/file/enums/file-category.enum';
import { SelectOption } from '@interfaces/select-option.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { File } from '@api/file/models/file.interface';
import { FileService } from '@api/file/services/file.service';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import { catchError, distinctUntilChanged, filter, tap } from 'rxjs';
import { genericToastError } from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SelectComponent } from '@components/form-inputs/select/select.component';
import { RouterLink } from '@angular/router';
import { ModalComponent } from '@components/modal/modal.component';
import { FileComponent } from '@components/file/file.component';
import { PaginationComponent } from '@components/pagination/pagination.component';

@Component({
  selector: 'files-index',
  imports: [
    SelectComponent,
    TranslocoDirective,
    ReactiveFormsModule,
    RouterLink,
    ModalComponent,
    FileComponent,
    PaginationComponent,
  ],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public category: FormControl<SelectOption<FileCategory> | null> =
    new FormControl(null);
  public response = signal<PaginatedResponse<File> | null>(null);
  public loading = signal<boolean>(false);
  public categories = signal<SelectOption<FileCategory>[] | null>(null);

  private fileService = inject(FileService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);

  public ngOnInit(): void {
    this.setCategories();
    this.loadFiles();
    this.listenToCategory();
  }

  public loadFiles(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.fileService
      .index({
        page,
        category: this.category.value?.value,
      })
      .pipe(
        filter((response): response is PaginatedResponse<File> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private listenToCategory(): void {
    this.category.valueChanges
      .pipe(
        distinctUntilChanged(),
        tap(() => {
          this.loadFiles();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private setCategories(): void {
    this.categories.set(
      Object.values(FileCategory).map(
        (category): SelectOption<FileCategory> => ({
          value: category,
          label: this.translocoService.translate(
            `enums.file_category.${category}`,
          ),
        }),
      ) as SelectOption<FileCategory>[],
    );
  }
}
