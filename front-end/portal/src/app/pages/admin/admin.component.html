<div class="h-full flex flex-col" *transloco="let t; read: 'pages.admin'">
  <!-- Navigation Header -->
  <div class="bg-white shadow-sm">
    <nav class="flex border-b border-gray-200 px-6">
      <div class="flex space-x-8">
        <!-- Files Tab -->
        <a
          routerLink="files"
          routerLinkActive="!text-adverge-blue !border-adverge-blue"
          class="text-gray-500 hover:text-gray-700 hover:border-gray-300 border-transparent group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 ease-in-out"
        >
          <i class="fas fa-file-alt mr-2"></i>
          {{ t('tabs.files') }}
        </a>

        <!-- Users Tab -->
        <a
          routerLink="users"
          routerLinkActive="!text-adverge-blue !border-adverge-blue"
          class="text-gray-500 hover:text-gray-700 hover:border-gray-300 border-transparent group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 ease-in-out"
        >
          <i class="fas fa-users mr-2"></i>
          {{ t('tabs.users') }}
        </a>
      </div>
    </nav>
  </div>

  <!-- Content Area -->
  <div class="flex-1 py-6">
    <router-outlet></router-outlet>
  </div>
</div>
