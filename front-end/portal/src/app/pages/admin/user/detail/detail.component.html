<div *transloco="let t; read: 'pages.admin.users.detail'" class="h-full">
  <div class="flex justify-between items-center w-full">
    <a routerLink="/admin/users" class="link space-x-2">
      <i class="fa-regular fa-arrow-left"></i>
      <span>{{ 'general.back' | transloco }}</span>
    </a>
    <button (click)="submit()" class="btn" [class.--loading]="saving()" [class.--disabled]="form()?.invalid">
      <i class="fa-regular fa-floppy-disk"></i>
      <span>{{ 'general.buttons.save' | transloco }}</span>
    </button>
  </div>

  <div class="flex-1 bg-adverge-light-gray">
    <div class="mx-auto py-8">
      @if (loading()) {
        <div class="flex items-center justify-center py-24">
          <div class="text-center">
            <i class="fa-duotone fa-solid fa-spinner-third text-3xl animate-spin text-adverge-blue mb-4"></i>
            <p class="text-adverge-gray">Loading user details...</p>
          </div>
        </div>
      } @else{
        @if(form(); as form) {
          <!-- Form Card -->
          <div class="bg-white rounded shadow-sm border border-gray-200">
            <div class="px-8 py-6 border-b border-gray-200">
              <h2 class="text-lg font-medium text-adverge-gray">User Information</h2>
              <p class="text-sm text-gray-600 mt-1">
                @if (user()) {
                  Update the user's information below.
                } @else {
                  Fill in the details to create a new user account.
                }
              </p>
            </div>

            <div class="px-8 py-8">
              <form [formGroup]="form" (ngSubmit)="submit()">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <!-- Left Column -->
                  <div class="space-y-6">
                    <!-- First Name -->
                    <div class="form-group">
                      <input
                        [formControl]="form.controls.firstname"
                        type="text"
                        autocomplete="given-name"
                      >
                      <label>{{ t('firstname') }}</label>
                    </div>

                    <!-- Email -->
                    <div class="form-group" [class.--without-disable-state]="false">
                      <input
                        [formControl]="form.controls.email"
                        type="email"
                        autocomplete="email"
                      >
                      <label>{{ t('email') }}</label>
                      @if (form.controls.email.disabled) {
                        <div class="mt-2 text-xs text-gray-500">
                          <i class="fa-regular fa-info-circle mr-1"></i>
                          Email cannot be changed for existing users
                        </div>
                      }
                    </div>
                  </div>

                  <!-- Right Column -->
                  <div class="space-y-6">
                    <!-- Last Name -->
                    <div class="form-group">
                      <input
                        [formControl]="form.controls.lastname"
                        type="text"
                        autocomplete="family-name"
                      >
                      <label>{{ t('lastname') }}</label>
                    </div>

                    <!-- Role -->
                    <div class="form-group">
                      <select [formControl]="form.controls.role">
                        <option value="" disabled>{{ t('role_placeholder') }}</option>
                        <option [value]="UserRole.ADMIN">{{ 'enums.user_role.ADMIN' | transloco }}</option>
                        <option [value]="UserRole.USER">{{ 'enums.user_role.USER' | transloco }}</option>
                      </select>
                      <label>{{ t('role') }}</label>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        }
      }
    </div>
  </div>
</div>
