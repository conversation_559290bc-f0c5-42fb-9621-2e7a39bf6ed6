import {
  Component,
  DestroyRef,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { UserRole } from '@api/profile/enums/user-role.enum';
import { User } from '@api/user/models/user.interface';
import { UserService } from '@api/user/services/user.service';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import { catchError, tap } from 'rxjs';
import {
  genericToastError,
  genericToastSuccess,
} from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UserStoreRequest } from '@api/user/requests/user-store.request';
import { UserRequest } from '@api/user/requests/user.request';
import { Router, RouterLink } from '@angular/router';

interface Form {
  firstname: FormControl<string | null>;
  lastname: FormControl<string | null>;
  email: FormControl<string | null>;
  role: FormControl<UserRole | null>;
}

@Component({
  selector: 'app-detail',
  imports: [TranslocoDirective, RouterLink, TranslocoPipe, ReactiveFormsModule],
  templateUrl: './detail.component.html',
})
export class DetailComponent implements OnInit {
  @Input('id') id?: number;
  public user = signal<User | null>(null);
  public loading = signal<boolean>(false);
  public saving = signal<boolean>(false);
  public form = signal<FormGroup<Form> | null>(null);
  public readonly UserRole = UserRole;

  private userService = inject(UserService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);

  public ngOnInit(): void {
    this.loadUser();
  }

  public submit(): void {
    const form = this.form();

    if (!form || form.invalid || this.saving()) {
      form?.markAllAsTouched();
      return;
    }

    this.saving.set(true);

    let service = this.userService.store(form.value as UserStoreRequest);

    const user = this.user();
    if (user) {
      service = this.userService.update(user, form.value as UserRequest);
    }

    service
      .pipe(
        tap((response) => {
          this.router.navigate(['admin', 'users']);
          genericToastSuccess(this.toastService, this.translocoService);
          this.saving.set(false);
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.saving.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadUser(): void {
    if (!this.id) {
      this.initForm();
      return;
    }

    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.userService
      .show(this.id)
      .pipe(
        tap((response) => {
          this.user.set(response.data);
          this.loading.set(false);
          this.initForm();
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.loading.set(false);
          this.initForm();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    const user = this.user();

    this.form.set(
      new FormGroup<Form>({
        firstname: new FormControl(user?.firstname ?? null, [
          Validators.required,
        ]),
        lastname: new FormControl(user?.lastname ?? null, [
          Validators.required,
        ]),
        email: new FormControl(
          { value: user?.email ?? null, disabled: !!user },
          [Validators.required],
        ),
        role: new FormControl(user?.role ?? null, [Validators.required]),
      }),
    );
  }
}
