<div *transloco="let t; read: 'pages.admin.user.index'" class="h-full flex flex-col">

  <div class="flex justify-between mb-4">
      <div class="flex items-center space-x-2 w-64">
        <i class="fa-regular fa-magnifying-glass text-lg"></i>
        <div class="form-group">
          <input [formControl]="search" [placeholder]="'general.search' | transloco">
        </div>
      </div>
    <a routerLink="detail" class="btn">
      <i class="fa-regular fa-plus"></i>
      <span>{{ 'general.buttons.add' | transloco }}</span>
    </a>
  </div>

  @if(loading()) {
    <div class="flex-1 flex items-center justify-center">
      <i class="fa-duotone fa-solid fa-spinner-third text-2xl animate-spin"></i>
    </div>
  } @else {
    @if(response(); as response) {
      <div class="flex-1 overflow-y-auto">
        @if(response.data.length > 0) {
          <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ t('table.id') }}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ t('table.name') }}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ t('table.email') }}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ t('table.role') }}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ t('table.last_login') }}
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ t('table.actions') }}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                @for(user of response.data; track user.id) {
                  <tr class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {{ user.id }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ user.firstname }} {{ user.lastname }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ user.email }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                            [class]="user.role === 'ADMIN' ? 'bg-adverge-blue bg-opacity-10 text-adverge-red' : 'bg-gray-100 text-gray-800'">
                        {{ 'enums.user_role.' + user.role | transloco }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      @if(user.last_login_at) {
                        {{ user.last_login_at | date:'short' }}
                      } @else {
                        <span class="text-gray-400 italic">{{ t('table.never_logged_in') }}</span>
                      }
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                      <a [routerLink]="['detail', user.id]"
                         class="text-adverge-blue hover:text-adverge-blue hover:opacity-80 transition-opacity duration-150">
                        <i class="fa-regular fa-edit mr-1"></i>
                        {{ 'general.buttons.edit' | transloco }}
                      </a>

                      <button
                        (click)="delete(user)"
                         class="text-red-500 hover:opacity-80 transition-opacity duration-150 cursor-pointer">
                        <i class="fa-regular fa-trash-can mr-1"></i>
                        {{ 'general.buttons.delete' | transloco }}
                      </button>
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        } @else {
          <div class="flex flex-col items-center justify-center py-12 text-center">
            <i class="fa-regular fa-users text-4xl text-gray-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p class="text-gray-500">{{ 'general.empty_placeholder' | transloco }}</p>
          </div>
        }
      </div>

      <div class="flex justify-center mt-6">
        <components-pagination [response]="response" (pageChange$)="loadUsers($event)"></components-pagination>
      </div>
    }
  }
</div>
