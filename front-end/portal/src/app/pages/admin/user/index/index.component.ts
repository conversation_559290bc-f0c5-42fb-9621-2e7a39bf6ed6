import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { User } from '@api/user/models/user.interface';
import { UserService } from '@api/user/services/user.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  tap,
} from 'rxjs';
import { genericToastError } from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RouterLink } from '@angular/router';
import { PaginationComponent } from '@components/pagination/pagination.component';
import { DatePipe } from '@angular/common';
import { DeleteService } from '@pages/admin/user/index/delete/delete.service';

@Component({
  selector: 'users-index',
  imports: [
    TranslocoDirective,
    ReactiveFormsModule,
    RouterLink,
    TranslocoPipe,
    PaginationComponent,
    DatePipe,
  ],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public response = signal<PaginatedResponse<User> | null>(null);
  public loading = signal<boolean>(false);
  public search: FormControl<string | null> = new FormControl(null);

  private userService = inject(UserService);
  private destroyRef = inject(DestroyRef);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private deleteService = inject(DeleteService);

  public ngOnInit(): void {
    this.loadUsers();
    this.listenToSearch();
  }

  public loadUsers(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.userService
      .index({ search: this.search.value, page })
      .pipe(
        filter((response): response is PaginatedResponse<User> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public async delete(user: User): Promise<void> {
    const result = await this.deleteService.show(user);

    if (!result) {
      return;
    }

    this.loadUsers(this.response()?.meta.current_page ?? 1);
  }

  private listenToSearch(): void {
    this.search.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadUsers();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
