import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ModalComponent } from '@app/components/modal/modal.component';
import {
  genericToastError,
  genericToastSuccess,
} from '@app/helpers/generic-toasts';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import { catchError, Subject, tap } from 'rxjs';
import { FileService } from '@api/file/services/file.service';
import { File } from '@api/file/models/file.interface';
import { User } from '@api/user/models/user.interface';
import { UserService } from '@api/user/services/user.service';

@Component({
  selector: 'app-delete',
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe],
  templateUrl: './delete.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteComponent {
  public user!: User;
  public loading = signal<boolean>(false);

  public close$: Subject<boolean> = new Subject();

  private userS = inject(UserService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.user || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.userS
      .delete(this.user)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.close(true);
          genericToastSuccess(this.toastService, this.translocoService);
        }),
        catchError((err) => {
          this.loading.set(false);
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
