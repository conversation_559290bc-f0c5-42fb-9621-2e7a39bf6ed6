import { inject, Injectable } from '@angular/core';
import { ModalService } from '@app/services/modal.service';
import { take, tap } from 'rxjs';
import { User } from '@api/user/models/user.interface';
import { DeleteComponent } from '@pages/admin/user/index/delete/delete.component';

@Injectable({
  providedIn: 'root',
})
export class DeleteService {
  private modalService = inject(ModalService);

  public show(user: User): Promise<boolean> {
    const modal = this.modalService.attach(DeleteComponent);

    modal.componentRef.instance.user = user;

    return new Promise((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value: boolean) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
