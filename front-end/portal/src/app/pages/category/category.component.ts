import {
  Component,
  DestroyRef,
  inject,
  Input,
  OnChanges,
  signal,
  SimpleChanges,
} from '@angular/core';
import { FileCategory } from '@api/file/enums/file-category.enum';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { File } from '@api/file/models/file.interface';
import { FileService } from '@api/file/services/file.service';
import { TranslocoService } from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import { catchError, filter, tap } from 'rxjs';
import { genericToastError } from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FileComponent } from '@components/file/file.component';
import { PaginationComponent } from '@components/pagination/pagination.component';

@Component({
  selector: 'app-category',
  imports: [FileComponent, PaginationComponent],
  templateUrl: './category.component.html',
})
export class CategoryComponent implements OnChanges {
  @Input('category') category!: FileCategory;
  public response = signal<PaginatedResponse<File> | null>(null);
  public loading = signal<boolean>(false);

  private fileService = inject(FileService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);

  public ngOnChanges(changes: SimpleChanges): void {
    if (!('category' in changes)) {
      return;
    }

    this.loadFiles();
  }

  public loadFiles(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(false);

    this.fileService
      .index({ page, category: this.category })
      .pipe(
        filter((response): response is PaginatedResponse<File> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
