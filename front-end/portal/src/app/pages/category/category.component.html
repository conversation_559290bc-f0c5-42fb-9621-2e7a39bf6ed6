@if(loading()) {
  <div class="flex-1 flex items-center justify-center">
    <i class="fa-duotone fa-solid fa-spinner-third text-2xl animate-spin"></i>
  </div>
} @else {
  @if(response(); as response) {
    <div class="flex-1 overflow-y-auto">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
        @for(file of response.data; track $index) {
          <components-file (reload$)="loadFiles(response.meta.current_page)" [file]="file" ></components-file>
        }
      </div>
    </div>

    <!-- Pagination centered at bottom -->
    <div class="flex justify-center mt-6">
      <components-pagination [response]="response" (pageChange$)="loadFiles($event)"></components-pagination>
    </div>
  }
}
