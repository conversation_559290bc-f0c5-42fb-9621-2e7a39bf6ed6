import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, Subject, tap } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { AuthenticationService } from '@api/auth/services/authentication.service';
import { Me } from '@api/auth/models/me.interface';
import { UserRole } from '@api/profile/enums/user-role.enum';

export const LOGGED_IN_LOCAL_STORAGE_KEY = 'logged_in';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  public user$: Subject<Me> = new Subject<Me>();

  private user: Me | undefined;
  private impersonating: boolean = false;

  constructor(
    private router: Router,
    private authenticationService: AuthenticationService,
  ) {}

  public setUser(user: Me): void {
    this.user = user;
  }

  public getUser(): Me | undefined {
    return this.user;
  }

  public isLoggedIn(): boolean {
    return !!localStorage.getItem(LOGGED_IN_LOCAL_STORAGE_KEY);
  }

  public isAdmin(): boolean {
    return this.user?.role === UserRole.ADMIN;
  }

  public logout(): void {
    this.user = undefined;

    this.authenticationService.logout().subscribe();
    localStorage.removeItem(LOGGED_IN_LOCAL_STORAGE_KEY);

    this.router.navigate(['/auth']);
  }

  public loadUser(): Observable<DataResponse<Me>> {
    return this.authenticationService.me().pipe(
      tap((response) => {
        this.setUser(response.data);
        this.user$.next(response.data);
      }),
    );
  }

  public isImpersonating(): boolean {
    return this.impersonating;
  }

  public setImpersonating(value: boolean): void {
    this.impersonating = value;
  }
}
