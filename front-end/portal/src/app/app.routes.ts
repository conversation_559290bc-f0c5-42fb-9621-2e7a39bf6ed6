import { Routes } from '@angular/router';
import { authGuard } from '@guards/auth.guard';
import { authenticatedUserResolver } from '@app/resolvers/authenticatedUserResolver';
import { ContainerComponent } from '@app/layouts/container/container.component';
import { adminGuard } from '@guards/admin.guard';
import { AdminComponent } from '@pages/admin/admin.component';
import { FileCategory } from '@api/file/enums/file-category.enum';

export const routes: Routes = [
  {
    path: '',
    canActivate: [authGuard],
    resolve: {
      me: authenticatedUserResolver,
    },
    component: ContainerComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'files/' + FileCategory.CAMPAIGNS,
      },
      {
        path: 'files/:category',
        loadComponent: () =>
          import('./pages/category/category.component').then(
            (c) => c.CategoryComponent,
          ),
      },
      {
        path: 'admin',
        canActivate: [adminGuard],
        component: AdminComponent,
        loadChildren: () =>
          import('./pages/admin/admin.routes').then((r) => r.routes),
      },
    ],
  },
  {
    path: 'auth',
    loadChildren: () =>
      import('./pages/auth/auth.routes').then((m) => m.AUTH_ROUTES),
  },
];
