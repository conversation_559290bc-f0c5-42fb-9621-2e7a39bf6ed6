<aside *transloco="let t; read: 'layouts.container.sidebar'" class="w-64 bg-white border-r border-gray-200 h-full">
  <nav class="px-4 py-6">
    <ul class="space-y-2">
      <!-- Campaigns -->
      <li>
        <a [routerLink]="['files', category.CAMPAIGNS]" routerLinkActive="!text-adverge-blue bg-adverge-light-gray" class="flex items-center px-3 py-2 text-sm font-medium text-adverge-secondary-gray hover:text-adverge-gray hover:bg-gray-50 rounded-md">
          <i class="fa-regular fa-bullhorn w-5 mr-3 text-center"></i>
          {{ 'enums.file_category.CAMPAIGNS' | transloco }}
        </a>
      </li>

      <!-- Ad groups -->
      <li>
        <a [routerLink]="['files', category.AD_GROUPS]" routerLinkActive="!text-adverge-blue bg-adverge-light-gray" class="flex items-center px-3 py-2 text-sm font-medium text-adverge-secondary-gray hover:text-adverge-gray hover:bg-gray-50 rounded-md">
          <i class="fa-regular fa-layer-group w-5 mr-3 text-center"></i>
          {{ 'enums.file_category.AD_GROUPS' | transloco }}
        </a>
      </li>

      <!-- Keywords -->
      <li>
        <a [routerLink]="['files', category.KEYWORDS]" routerLinkActive="!text-adverge-blue bg-adverge-light-gray" class="flex items-center px-3 py-2 text-sm font-medium text-adverge-secondary-gray hover:text-adverge-gray hover:bg-gray-50 rounded-md">
          <i class="fa-regular fa-key w-5 mr-3 text-center"></i>
          {{ 'enums.file_category.KEYWORDS' | transloco }}
        </a>
      </li>

      <!-- Search terms -->
      <li>
        <a [routerLink]="['files', category.SEARCH_TERMS]" routerLinkActive="!text-adverge-blue bg-adverge-light-gray" class="flex items-center px-3 py-2 text-sm font-medium text-adverge-secondary-gray hover:text-adverge-gray hover:bg-gray-50 rounded-md">
          <i class="fa-regular fa-magnifying-glass w-5 mr-3 text-center"></i>
          {{ 'enums.file_category.SEARCH_TERMS' | transloco }}
        </a>
      </li>

      <!-- Audiences -->
      <li>
        <a [routerLink]="['files', category.AUDIENCES]" routerLinkActive="!text-adverge-blue bg-adverge-light-gray" class="flex items-center px-3 py-2 text-sm font-medium text-adverge-secondary-gray hover:text-adverge-gray hover:bg-gray-50 rounded-md">
          <i class="fa-regular fa-users w-5 mr-3 text-center"></i>
          {{ 'enums.file_category.AUDIENCES' | transloco }}
        </a>
      </li>

      <!-- Ads -->
      <li>
        <a [routerLink]="['files', category.ADS]" routerLinkActive="!text-adverge-blue bg-adverge-light-gray" class="flex items-center px-3 py-2 text-sm font-medium text-adverge-secondary-gray hover:text-adverge-gray hover:bg-gray-50 rounded-md">
          <i class="fa-regular fa-rectangle-ad w-5 mr-3 text-center"></i>
          {{ 'enums.file_category.ADS' | transloco }}
        </a>
      </li>

      <!-- Other -->
      <li>
        <a [routerLink]="['files', category.OTHER]" routerLinkActive="!text-adverge-blue bg-adverge-light-gray" class="flex items-center px-3 py-2 text-sm font-medium text-adverge-secondary-gray hover:text-adverge-gray hover:bg-gray-50 rounded-md">
          <i class="fa-regular fa-ellipsis w-5 mr-3 text-center"></i>
          {{ 'enums.file_category.OTHER' | transloco }}
        </a>
      </li>

      @if(authService.isAdmin()) {
        <li>
          <a routerLink="admin" routerLinkActive="!text-adverge-blue bg-adverge-light-gray" class="flex items-center px-3 py-2 text-sm font-medium text-adverge-secondary-gray hover:text-adverge-gray hover:bg-gray-50 rounded-md">
            <i class="fa-regular fa-gear w-5 mr-3 text-center"></i>
            {{ t('admin') }}
          </a>
        </li>
      }
    </ul>
  </nav>
</aside>
