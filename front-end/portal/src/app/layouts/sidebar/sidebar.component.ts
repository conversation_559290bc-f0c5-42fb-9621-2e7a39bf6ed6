import { Component, inject } from '@angular/core';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { FileCategory } from '@api/file/enums/file-category.enum';
import { AuthService } from '@services/auth.service';

@Component({
  selector: 'app-sidebar',
  imports: [TranslocoDirective, RouterLink, RouterLinkActive, TranslocoPipe],
  templateUrl: './sidebar.component.html',
})
export class SidebarComponent {
  public readonly category = FileCategory;

  public authService = inject(AuthService);
}
