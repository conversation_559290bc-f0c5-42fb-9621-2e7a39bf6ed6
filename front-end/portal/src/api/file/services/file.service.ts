import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { FileIndexRequest } from '@api/file/requests/file-index.request';
import { DataResponse } from '@api/support/responses/data.response';
import { File } from '@api/file/models/file.interface';
import { Observable } from 'rxjs';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@helpers/transform-params';
import { FileRequest } from '@api/file/requests/file.request';
import { bodyToFormData } from '@helpers/body-to-form-data';

@Injectable({
  providedIn: 'root',
})
export class FileService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: FileIndexRequest,
  ): Observable<DataResponse<File[]> | PaginatedResponse<File>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<DataResponse<File[]> | PaginatedResponse<File>>(
      `${this.endpoint}/api/v1/files`,
      { params },
    );
  }

  public store(data: FileRequest): Observable<DataResponse<File>> {
    const body = bodyToFormData(data);

    return this.httpClient.post<DataResponse<File>>(
      `${this.endpoint}/api/v1/admin/files`,
      body,
    );
  }

  public delete(file: File): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/v1/admin/files/${file.id}`,
    );
  }
}
