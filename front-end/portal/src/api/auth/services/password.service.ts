import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { PasswordResetRequest } from '@api/auth/requests/password-reset.request';

@Injectable({
  providedIn: 'root',
})
export class PasswordService {
  private readonly endpoint: string | undefined;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public forgot(email: string): Observable<void> {
    return this.httpClient.post<void>(
      this.endpoint + '/api/v1/auth/password/forgot',
      { email },
    );
  }

  public reset(body: PasswordResetRequest): Observable<void> {
    return this.httpClient.post<void>(
      this.endpoint + '/api/v1/auth/password/reset',
      body,
    );
  }
}
