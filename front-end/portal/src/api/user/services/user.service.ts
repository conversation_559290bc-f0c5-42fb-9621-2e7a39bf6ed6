import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IndexRequest } from '@api/support/requests/index.request';
import { DataResponse } from '@api/support/responses/data.response';
import { ConfigService } from '@app/services/config.service';
import { Observable } from 'rxjs';
import { User } from '../models/user.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@app/helpers/transform-params';
import { UserStoreRequest } from '../requests/user-store.request';
import { UserRequest } from '../requests/user.request';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private readonly endpoint?: string;

  constructor(
    private httpClient: HttpClient,
    private configService: ConfigService,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: IndexRequest,
  ): Observable<DataResponse<User[]> | PaginatedResponse<User>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<DataResponse<User[]> | PaginatedResponse<User>>(
      `${this.endpoint}/api/v1/admin/users`,
      { params },
    );
  }

  public show(id: number): Observable<DataResponse<User>> {
    return this.httpClient.get<DataResponse<User>>(
      `${this.endpoint}/api/v1/admin/users/${id}`,
    );
  }

  public store(body: UserStoreRequest): Observable<DataResponse<User>> {
    return this.httpClient.post<DataResponse<User>>(
      `${this.endpoint}/api/v1/admin/users`,
      body,
    );
  }

  public update(user: User, body: UserRequest): Observable<DataResponse<User>> {
    return this.httpClient.put<DataResponse<User>>(
      `${this.endpoint}/api/v1/admin/users/${user.id}`,
      body,
    );
  }

  public delete(user: User): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/v1/admin/users/${user.id}`,
    );
  }
}
